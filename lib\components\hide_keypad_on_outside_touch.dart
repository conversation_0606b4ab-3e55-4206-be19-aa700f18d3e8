
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

class HideKeypadOnOutsideTouch extends StatelessWidget {
  final Widget child;

  const HideKeypadOnOutsideTouch({super.key, 
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
      child: child,
    );
  }
}
