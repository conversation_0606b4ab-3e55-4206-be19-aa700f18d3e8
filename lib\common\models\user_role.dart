enum UserRole {
  admin('admin', 'Admin'),
  promoter('promoter', 'Promoter'),
  user('user', 'User');

  const UserRole(this.value, this.displayName);

  final String value;
  final String displayName;

  static UserRole fromString(String value) {
    return UserRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => UserRole.user,
    );
  }

  @override
  String toString() => value;
}
