import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../controllers/auth_provider.dart';
import '../../utils/colors.dart';
import '../../utils/app_text.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    // Use addPostFrameCallback to ensure this runs after the build phase
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final authProvider = context.read<AuthProvider>();
      await authProvider.initializeAuth();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kPrimaryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo/Icon
            Container(
              width: 120.w,
              height: 120.w,
              decoration: BoxDecoration(
                color: kWhiteColor,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Icon(Icons.security, size: 60.sp, color: kPrimaryColor),
            ),

            SizedBox(height: 30.h),

            // App Name
            Text(
              AppText.appName,
              style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.bold,
                color: kWhiteColor,
              ),
            ),

            SizedBox(height: 10.h),

            // Tagline
            Text(
              'Secure Access Management',
              style: TextStyle(
                fontSize: 16.sp,
                color: kWhiteColor.withOpacity(0.8),
              ),
            ),

            SizedBox(height: 50.h),

            // Loading Indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(kWhiteColor),
            ),

            SizedBox(height: 20.h),

            Text(
              'Loading...',
              style: TextStyle(
                fontSize: 14.sp,
                color: kWhiteColor.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
