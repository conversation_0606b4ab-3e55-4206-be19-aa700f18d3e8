import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:gatepass_flutter/utils/colors.dart';

void showLoader() {
  EasyLoading.show(maskType: EasyLoadingMaskType.none, dismissOnTap: false, indicator: customProgressIndicator());
}

void hideLoader() {
  EasyLoading.dismiss();
}

Widget customProgressIndicator() {
  return CircularProgressIndicator(
    valueColor: AlwaysStoppedAnimation<Color>(kPrimaryColor),
  );
}
