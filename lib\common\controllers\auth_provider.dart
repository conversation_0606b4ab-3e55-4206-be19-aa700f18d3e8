import 'package:flutter/foundation.dart';
import '../models/auth_state.dart';
import '../models/user_model.dart';
import '../models/user_role.dart';
import '../../services/auth_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  AuthState _state = const AuthState();
  bool _isInitialized = false;

  AuthState get state => _state;
  UserModel? get currentUser => _state.user;
  bool get isAuthenticated => _state.isAuthenticated;
  bool get isLoading => _state.isLoading;
  String? get errorMessage => _state.errorMessage;
  bool get isInitialized => _isInitialized;

  /// Initialize auth state by checking if user is already logged in
  Future<void> initializeAuth() async {
    _updateState(_state.copyWith(status: AuthStatus.loading));

    try {
      final user = await _authService.getCurrentUser();
      if (user != null) {
        _updateState(
          _state.copyWith(
            status: AuthStatus.authenticated,
            user: user,
            errorMessage: null,
          ),
        );
      } else {
        _updateState(
          _state.copyWith(
            status: AuthStatus.unauthenticated,
            user: null,
            errorMessage: null,
          ),
        );
      }
    } catch (e) {
      _updateState(
        _state.copyWith(
          status: AuthStatus.unauthenticated,
          user: null,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  /// Login with email, password and role
  Future<bool> login(String email, String password, UserRole role) async {
    _updateState(_state.copyWith(status: AuthStatus.loading));

    try {
      final user = await _authService.login(email, password, role);
      if (user != null) {
        _updateState(
          _state.copyWith(
            status: AuthStatus.authenticated,
            user: user,
            errorMessage: null,
          ),
        );
        return true;
      } else {
        _updateState(
          _state.copyWith(
            status: AuthStatus.unauthenticated,
            user: null,
            errorMessage: 'Login failed',
          ),
        );
        return false;
      }
    } catch (e) {
      _updateState(
        _state.copyWith(
          status: AuthStatus.error,
          user: null,
          errorMessage: e.toString(),
        ),
      );
      return false;
    }
  }

  /// Logout current user
  Future<void> logout() async {
    _updateState(_state.copyWith(status: AuthStatus.loading));

    try {
      await _authService.logout();
      _updateState(
        _state.copyWith(
          status: AuthStatus.unauthenticated,
          user: null,
          errorMessage: null,
        ),
      );
    } catch (e) {
      _updateState(
        _state.copyWith(status: AuthStatus.error, errorMessage: e.toString()),
      );
    }
  }

  /// Clear error message
  void clearError() {
    if (_state.hasError) {
      _updateState(
        _state.copyWith(status: AuthStatus.unauthenticated, errorMessage: null),
      );
    }
  }

  /// Get demo credentials for testing
  Map<String, String> getDemoCredentials(UserRole role) {
    return _authService.getDemoCredentials(role);
  }

  /// Check if email is valid
  bool isValidEmail(String email) {
    return _authService.isValidEmail(email);
  }

  /// Update state and notify listeners
  void _updateState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }
}
