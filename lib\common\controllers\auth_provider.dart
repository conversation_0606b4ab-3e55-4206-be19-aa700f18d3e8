import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../models/user_role.dart';
import '../../services/auth_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();

  UserModel? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;
  bool _isInitialized = false;

  UserModel? get currentUser => _currentUser;
  bool get isAuthenticated => _currentUser != null;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isInitialized => _isInitialized;

  /// Initialize auth state by checking if user is already logged in
  Future<void> initializeAuth() async {
    // Prevent multiple initialization calls
    if (_isInitialized || _isLoading) {
      return;
    }

    _setLoading(true);

    try {
      final user = await _authService.getCurrentUser();
      _currentUser = user;
      _errorMessage = null;
    } catch (e) {
      _currentUser = null;
      _errorMessage = e.toString();
    } finally {
      _isInitialized = true;
      _setLoading(false);
    }
  }

  /// Login with email, password and role
  Future<bool> login(String email, String password, UserRole role) async {
    _setLoading(true);
    _errorMessage = null;

    try {
      final user = await _authService.login(email, password, role);
      if (user != null) {
        _currentUser = user;
        _setLoading(false);
        return true;
      } else {
        _currentUser = null;
        _errorMessage = 'Login failed';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _currentUser = null;
      _errorMessage = e.toString();
      _setLoading(false);
      return false;
    }
  }

  /// Logout current user
  Future<void> logout() async {
    _setLoading(true);

    try {
      await _authService.logout();
      _currentUser = null;
      _errorMessage = null;
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  /// Clear error message
  void clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  /// Get demo credentials for testing
  Map<String, String> getDemoCredentials(UserRole role) {
    return _authService.getDemoCredentials(role);
  }

  /// Check if email is valid
  bool isValidEmail(String email) {
    return _authService.isValidEmail(email);
  }

  /// Set loading state and notify listeners
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
