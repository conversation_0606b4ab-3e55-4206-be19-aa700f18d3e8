import 'dart:convert';
import 'package:gatepass_flutter/main.dart';
import '../common/models/user_model.dart';
import '../common/models/user_role.dart';
import '../utils/hive_keys.dart';

class AuthService {
  // Get Hive box

  // Mock user data for demonstration
  static final Map<String, Map<String, dynamic>> _mockUsers = {
    // Admin users
    '<EMAIL>': {
      'id': '1',
      'email': '<EMAIL>',
      'password': 'admin123',
      'name': 'Admin User',
      'role': 'admin',
      'isActive': true,
    },
    '<EMAIL>': {
      'id': '2',
      'email': '<EMAIL>',
      'password': 'admin123',
      'name': 'Super Admin',
      'role': 'admin',
      'isActive': true,
    },

    // Promoter users
    '<EMAIL>': {
      'id': '3',
      'email': '<EMAIL>',
      'password': 'promoter123',
      'name': 'Event Promoter',
      'role': 'promoter',
      'isActive': true,
    },
    '<EMAIL>': {
      'id': '4',
      'email': '<EMAIL>',
      'password': 'promoter123',
      'name': 'Music Promoter',
      'role': 'promoter',
      'isActive': true,
    },

    // Regular users
    '<EMAIL>': {
      'id': '5',
      'email': '<EMAIL>',
      'password': 'user123',
      'name': 'Regular User',
      'role': 'user',
      'isActive': true,
    },
    '<EMAIL>': {
      'id': '6',
      'email': '<EMAIL>',
      'password': 'user123',
      'name': 'Customer User',
      'role': 'user',
      'isActive': true,
    },
  };

  /// Login with email, password and role
  Future<UserModel?> login(String email, String password, UserRole role) async {
    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      final userData = _mockUsers[email.toLowerCase()];

      if (userData == null) {
        throw Exception('User not found');
      }

      if (userData['password'] != password) {
        throw Exception('Invalid password');
      }

      if (userData['role'] != role.value) {
        throw Exception('Invalid role for this user');
      }

      if (userData['isActive'] != true) {
        throw Exception('Account is deactivated');
      }

      // Create user model
      final user = UserModel.fromJson({
        ...userData,
        'lastLogin': DateTime.now().toIso8601String(),
      });

      // Save user data locally
      await _saveUserData(user);

      return user;
    } catch (e) {
      rethrow;
    }
  }

  /// Get current logged in user
  Future<UserModel?> getCurrentUser() async {
    try {
      final isLoggedIn = box.get(HiveKeys.isLoggedInKey, defaultValue: false);

      if (!isLoggedIn) return null;

      final userJson = box.get(HiveKeys.userKey);
      if (userJson == null) return null;

      final userData = jsonDecode(userJson);
      return UserModel.fromJson(userData);
    } catch (e) {
      return null;
    }
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    return box.get(HiveKeys.isLoggedInKey, defaultValue: false);
  }

  /// Logout user
  Future<void> logout() async {
    await box.delete(HiveKeys.userKey);
    await box.put(HiveKeys.isLoggedInKey, false);
  }

  /// Save user data to local storage
  Future<void> _saveUserData(UserModel user) async {
    await box.put(HiveKeys.userKey, jsonEncode(user.toJson()));
    await box.put(HiveKeys.isLoggedInKey, true);
  }

  /// Get users by role (for testing/demo purposes)
  List<Map<String, dynamic>> getUsersByRole(UserRole role) {
    return _mockUsers.values
        .where((user) => user['role'] == role.value)
        .toList();
  }

  /// Validate email format
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Get demo credentials for each role
  Map<String, String> getDemoCredentials(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return {'email': '<EMAIL>', 'password': 'admin123'};
      case UserRole.promoter:
        return {'email': '<EMAIL>', 'password': 'promoter123'};
      case UserRole.user:
        return {'email': '<EMAIL>', 'password': 'user123'};
    }
  }
}
