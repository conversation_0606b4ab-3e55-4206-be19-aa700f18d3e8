import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../common/controllers/auth_provider.dart';
import '../common/models/user_role.dart';
import '../common/screens/role_selection_screen.dart';
import '../common/screens/splash_screen.dart';

// Import screens (will be created in next steps)
import '../admin/screens/auth/admin_login_screen.dart';
import '../admin/screens/dashboard/admin_dashboard_screen.dart';
import '../promoter/screens/auth/promoter_login_screen.dart';
import '../promoter/screens/dashboard/promoter_dashboard_screen.dart';
import '../customer/screens/auth/user_login_screen.dart';
import '../customer/screens/dashboard/user_dashboard_screen.dart';

class AppRouter {
  static const String splash = '/';
  static const String roleSelection = '/role-selection';
  
  // Admin routes
  static const String adminLogin = '/admin/login';
  static const String adminDashboard = '/admin/dashboard';
  
  // Promoter routes
  static const String promoterLogin = '/promoter/login';
  static const String promoterDashboard = '/promoter/dashboard';
  
  // User routes
  static const String userLogin = '/user/login';
  static const String userDashboard = '/user/dashboard';

  static GoRouter createRouter(AuthProvider authProvider) {
    return GoRouter(
      initialLocation: splash,
      refreshListenable: authProvider,
      redirect: (context, state) {
        final isAuthenticated = authProvider.isAuthenticated;
        final currentUser = authProvider.currentUser;
        final isLoading = authProvider.isLoading;

        // Show splash while loading
        if (isLoading) {
          return splash;
        }

        // If not authenticated, redirect to role selection or login
        if (!isAuthenticated) {
          final currentPath = state.matchedLocation;
          
          // Allow access to splash, role selection, and login screens
          if (currentPath == splash || 
              currentPath == roleSelection ||
              currentPath == adminLogin ||
              currentPath == promoterLogin ||
              currentPath == userLogin) {
            return null; // No redirect needed
          }
          
          return roleSelection;
        }

        // If authenticated, redirect to appropriate dashboard
        if (isAuthenticated && currentUser != null) {
          final currentPath = state.matchedLocation;
          
          // If on login screens, redirect to dashboard
          if (currentPath == adminLogin || 
              currentPath == promoterLogin || 
              currentPath == userLogin ||
              currentPath == roleSelection ||
              currentPath == splash) {
            
            switch (currentUser.role) {
              case UserRole.admin:
                return adminDashboard;
              case UserRole.promoter:
                return promoterDashboard;
              case UserRole.user:
                return userDashboard;
            }
          }

          // Check if user is trying to access wrong role's area
          switch (currentUser.role) {
            case UserRole.admin:
              if (currentPath.startsWith('/promoter') || currentPath.startsWith('/user')) {
                return adminDashboard;
              }
              break;
            case UserRole.promoter:
              if (currentPath.startsWith('/admin') || currentPath.startsWith('/user')) {
                return promoterDashboard;
              }
              break;
            case UserRole.user:
              if (currentPath.startsWith('/admin') || currentPath.startsWith('/promoter')) {
                return userDashboard;
              }
              break;
          }
        }

        return null; // No redirect needed
      },
      routes: [
        GoRoute(
          path: splash,
          builder: (context, state) => const SplashScreen(),
        ),
        GoRoute(
          path: roleSelection,
          builder: (context, state) => const RoleSelectionScreen(),
        ),
        
        // Admin routes
        GoRoute(
          path: adminLogin,
          builder: (context, state) => const AdminLoginScreen(),
        ),
        GoRoute(
          path: adminDashboard,
          builder: (context, state) => const AdminDashboardScreen(),
        ),
        
        // Promoter routes
        GoRoute(
          path: promoterLogin,
          builder: (context, state) => const PromoterLoginScreen(),
        ),
        GoRoute(
          path: promoterDashboard,
          builder: (context, state) => const PromoterDashboardScreen(),
        ),
        
        // User routes
        GoRoute(
          path: userLogin,
          builder: (context, state) => const UserLoginScreen(),
        ),
        GoRoute(
          path: userDashboard,
          builder: (context, state) => const UserDashboardScreen(),
        ),
      ],
      errorBuilder: (context, state) => Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('Page not found: ${state.matchedLocation}'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go(roleSelection),
                child: const Text('Go Home'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
