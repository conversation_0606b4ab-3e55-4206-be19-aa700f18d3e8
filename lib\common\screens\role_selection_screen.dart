import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../models/user_role.dart';
import '../../utils/colors.dart';
import '../../utils/app_text.dart';
import '../../utils/app_router.dart';

class RoleSelectionScreen extends StatelessWidget {
  const RoleSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kWhiteColor,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            children: [
              SizedBox(height: 40.h),
              
              // Header
              Text(
                'Welcome to ${AppText.appName}',
                style: TextStyle(
                  fontSize: 28.sp,
                  fontWeight: FontWeight.bold,
                  color: kTextColor,
                ),
                textAlign: TextAlign.center,
              ),
              
              SizedBox(height: 10.h),
              
              Text(
                'Please select your role to continue',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: kTextColor.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
              
              SizedBox(height: 60.h),
              
              // Role Cards
              Expanded(
                child: Column(
                  children: [
                    _RoleCard(
                      role: UserRole.admin,
                      icon: Icons.admin_panel_settings,
                      title: 'Admin',
                      subtitle: 'Manage system and users',
                      color: Colors.red,
                      onTap: () => context.go(AppRouter.adminLogin),
                    ),
                    
                    SizedBox(height: 20.h),
                    
                    _RoleCard(
                      role: UserRole.promoter,
                      icon: Icons.event,
                      title: 'Promoter',
                      subtitle: 'Manage events and promotions',
                      color: Colors.orange,
                      onTap: () => context.go(AppRouter.promoterLogin),
                    ),
                    
                    SizedBox(height: 20.h),
                    
                    _RoleCard(
                      role: UserRole.user,
                      icon: Icons.person,
                      title: 'User',
                      subtitle: 'Access events and services',
                      color: Colors.blue,
                      onTap: () => context.go(AppRouter.userLogin),
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: 20.h),
              
              // Footer
              Text(
                'Choose the role that matches your account type',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: kTextColor.withOpacity(0.5),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _RoleCard extends StatelessWidget {
  final UserRole role;
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  const _RoleCard({
    required this.role,
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          padding: EdgeInsets.all(24.w),
          child: Row(
            children: [
              Container(
                width: 60.w,
                height: 60.w,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  icon,
                  size: 30.sp,
                  color: color,
                ),
              ),
              
              SizedBox(width: 20.w),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                        color: kTextColor,
                      ),
                    ),
                    
                    SizedBox(height: 4.h),
                    
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: kTextColor.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
              
              Icon(
                Icons.arrow_forward_ios,
                size: 20.sp,
                color: kTextColor.withOpacity(0.3),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
